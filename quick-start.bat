@echo off
echo ========================================
echo Quick Start - Healthy Diet Platform
echo ========================================

echo Starting Docker services...
docker-compose up -d

echo.
echo Waiting 30 seconds for Docker services...
timeout /t 30 /nobreak

echo.
echo Starting Gateway Service...
cd /d "%~dp0health-backend\gateway-service"
start "Gateway" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting Auth Service...
cd /d "%~dp0health-backend\auth-service"
start "Auth" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting User Service...
cd /d "%~dp0health-backend\user-service"
start "User" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting Recipe Service...
cd /d "%~dp0health-backend\recipe-service"
start "Recipe" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting Plan Service...
cd /d "%~dp0health-backend\plan-service"
start "Plan" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting Admin Service...
cd /d "%~dp0health-backend\admin-service"
start "Admin" cmd /k "mvn spring-boot:run"
timeout /t 5 /nobreak

echo Starting AIGC Service...
cd /d "%~dp0health-backend\aigc-service"
start "AIGC" cmd /k "mvn spring-boot:run"

echo.
echo ========================================
echo All services are starting...
echo Please wait 2-3 minutes for completion
echo ========================================
echo.
echo Access URLs:
echo - Nacos: http://localhost:8848/nacos
echo - Gateway: http://localhost:8080
echo.

pause
