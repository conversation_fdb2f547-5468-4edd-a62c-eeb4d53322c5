# ===============================================
# user-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8082 # 用户服务的监听端口

spring:
  application:
    name: user-service # 应用名

  # --- 导入Nacos配置 ---
  config:
    import: "optional:nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848
      config:
        server-addr: nacos:8848

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    # 你的项目包名
    com.healthdiet.user: debug
    com.baomidou.mybatisplus: debug

management:
  endpoints:
    web:
      exposure:
        include: "*"