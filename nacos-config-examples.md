# Nacos配置中心配置示例

## 重要说明

由于后端服务运行在主机上，而MySQL运行在Docker容器中，需要在Nacos配置中心为每个服务配置正确的数据库连接信息。

## 配置步骤

1. 启动Docker服务：`start-docker-services.bat`
2. 访问Nacos管理界面：http://localhost:8848/nacos
3. 登录：用户名/密码 = nacos/nacos
4. 进入"配置管理" → "配置列表"
5. 为每个服务创建配置文件

## 服务配置示例

### 1. auth-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 2. user-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 3. recipe-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 4. gateway-service.yml

```yaml
# 网关路由配置
spring:
  cloud:
    gateway:
      routes:
        # 认证服务路由
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1

        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1

        # 菜谱服务路由
        - id: recipe-service
          uri: lb://recipe-service
          predicates:
            - Path=/recipe/**
          filters:
            - StripPrefix=1

        # 计划服务路由
        - id: plan-service
          uri: lb://plan-service
          predicates:
            - Path=/plan/**
          filters:
            - StripPrefix=1

        # AIGC服务路由
        - id: aigc-service
          uri: lb://aigc-service
          predicates:
            - Path=/aigc/**
          filters:
            - StripPrefix=1

      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
```

### 5. plan-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 6. aigc-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 7. admin-service.yml

```yaml
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
```

## 配置要点

1. **数据库连接**：使用`localhost:3307`（Docker端口映射）
2. **Redis连接**：使用`localhost:6380`（Docker端口映射）
3. **RabbitMQ连接**：使用`localhost:5672`（Docker端口映射）
4. **字符编码**：确保URL中包含`characterEncoding=utf8`
5. **时区设置**：使用`serverTimezone=Asia/Shanghai`

## 注意事项

- 每个配置文件的Data ID必须与服务名一致（如：auth-service.yml）
- Group保持默认的DEFAULT_GROUP
- 配置格式选择YAML
- 配置完成后，重启对应的后端服务使配置生效
