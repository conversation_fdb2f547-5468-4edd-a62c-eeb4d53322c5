# aigc-service.yml - Nacos配置中心完整配置文件
# 数据源配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password: 
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5673
    username: guest
    password: guest

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# DeepSeek API配置
deepseek:
  api:
    key: "your-deepseek-api-key-here"
    base-url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    timeout: 30000
    max-tokens: 2000

# 日志配置
logging:
  level:
    com.healthdiet.aigc: debug
    com.baomidou.mybatisplus: debug
    org.springframework.amqp: info
    org.springframework.data.redis: debug
