# ===============================================
# gateway-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8080 # 网关服务的统一入口端口

spring:
  application:
    name: gateway-service # 应用名

  # --- 导入Nacos配置 ---
  config:
    import: "nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: true  # 启用服务注册
      config:
        server-addr: localhost:8848

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    com.healthdiet.gateway: debug
    # 网关的日志建议设为info，只有在排查具体路由问题时才开debug，否则日志量太大
    org.springframework.cloud.gateway: info

management:
  endpoints:
    web:
      exposure:
        include: "*"