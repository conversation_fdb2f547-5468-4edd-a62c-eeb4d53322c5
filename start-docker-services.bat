@echo off
echo ========================================
echo 启动健康饮食平台Docker服务
echo ========================================

echo 正在启动Docker服务...
docker-compose up -d

echo.
echo 等待服务启动完成...
timeout /t 30 /nobreak

echo.
echo ========================================
echo 服务状态检查
echo ========================================
docker-compose ps

echo.
echo ========================================
echo 服务访问地址
echo ========================================
echo MySQL:     localhost:3307
echo Redis:     localhost:6380  
echo RabbitMQ:  localhost:15672 (管理界面)
echo Nacos:     localhost:8848 (管理界面)
echo ========================================

echo.
echo 重要提示：
echo 1. Nacos管理界面: http://localhost:8848/nacos
echo    默认用户名/密码: nacos/nacos
echo 2. RabbitMQ管理界面: http://localhost:15672
echo    默认用户名/密码: guest/guest
echo 3. 首次使用需要在Nacos中配置数据库连接
echo    参考文件: nacos-config-examples.md
echo 4. 配置完成后再启动后端服务
echo.

pause
