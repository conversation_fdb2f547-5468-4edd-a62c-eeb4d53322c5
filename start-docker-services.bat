@echo off
echo ========================================
echo Starting Docker Infrastructure Services
echo ========================================

echo Starting Docker services...
docker-compose up -d

echo.
echo Waiting for services to start...
timeout /t 30 /nobreak

echo.
echo ========================================
echo Service Status Check
echo ========================================
docker-compose ps

echo.
echo ========================================
echo Service Access Information
echo ========================================
echo MySQL:     localhost:3307
echo Redis:     localhost:6380  
echo Nacos:     localhost:8848 (Management UI)
echo ========================================

echo.
echo Important Notes:
echo 1. Nacos Management UI: http://localhost:8848/nacos
echo    Default Username/Password: nacos/nacos
echo 2. First time setup requires Nacos configuration
echo 3. Configure all service configs in Nacos before starting microservices
echo.

pause
