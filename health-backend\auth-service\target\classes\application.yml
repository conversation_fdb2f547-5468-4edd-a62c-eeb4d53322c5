# ===============================================
# auth-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8081 # 认证服务的监听端口

spring:
  application:
    name: auth-service # 应用名

  # --- 导入Nacos配置 ---
  # 取消这行的注释，并把它放在spring.config下
  config:
    import: "optional:nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        # file-extension: yml # 在import中指定了后缀，这里可以省略

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    # 你的项目包名
    com.healthdiet.auth: debug
    # 在开发时可以开启MyBatis日志看SQL
    com.baomidou.mybatisplus: debug

management:
  endpoints:
    web:
      exposure:
        include: "*"