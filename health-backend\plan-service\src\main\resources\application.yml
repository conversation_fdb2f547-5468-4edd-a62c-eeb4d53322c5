# ===============================================
# plan-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8084 # 饮食计划服务的监听端口

spring:
  application:
    name: plan-service # 应用名

  # --- 导入Nacos配置 ---
  config:
    import: "optional:nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848
      config:
        server-addr: nacos:8848

# --- 本地固定的日志配置 ---
logging:
  level:
    # 你的项目包名
    com.healthdiet.plan: debug
    org.springframework.cloud: info