# ===============================================
# 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8085 # 应用的监听端口

spring:
  application:
    name: admin-service # 定义应用名，用于服务注册和配置拉取

  # --- 核心修改：将 'import' 放在 'spring.config' 下 ---
  config:
    import:
      # 告诉Spring Boot去Nacos加载一个名为 'admin-service.yml' 的配置文件
      - nacos:${spring.application.name}.yml

  # --- Nacos客户端的地址配置 ---
  cloud:
    nacos:
      # 服务发现中心的地址
      discovery:
        server-addr: localhost:8848
        enabled: false  # 暂时禁用服务注册
      # 配置中心的地址
      config:
        server-addr: localhost:8848

  # --- 临时本地数据库配置 ---
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    com.healthdiet.admin: debug
    com.baomidou.mybatisplus: debug

management:
  endpoints:
    web:
      exposure:
        include: "*"