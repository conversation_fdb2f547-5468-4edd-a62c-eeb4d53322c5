<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.healthdiet.auth.AuthApplicationTest" time="2.88" tests="1" errors="1" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\test-classes;C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-web\3.0.2\spring-boot-starter-web-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter\3.0.2\spring-boot-starter-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot\3.0.2\spring-boot-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-logging\3.0.2\spring-boot-starter-logging-3.0.2.jar;D:\Develop\maven\local\ch\qos\logback\logback-classic\1.4.5\logback-classic-1.4.5.jar;D:\Develop\maven\local\ch\qos\logback\logback-core\1.4.5\logback-core-1.4.5.jar;D:\Develop\maven\local\org\apache\logging\log4j\log4j-to-slf4j\2.19.0\log4j-to-slf4j-2.19.0.jar;D:\Develop\maven\local\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;D:\Develop\maven\local\org\slf4j\jul-to-slf4j\2.0.6\jul-to-slf4j-2.0.6.jar;D:\Develop\maven\local\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-json\3.0.2\spring-boot-starter-json-3.0.2.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-databind\2.14.1\jackson-databind-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-annotations\2.14.1\jackson-annotations-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.14.1\jackson-datatype-jdk8-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.14.1\jackson-datatype-jsr310-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\module\jackson-module-parameter-names\2.14.1\jackson-module-parameter-names-2.14.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-tomcat\3.0.2\spring-boot-starter-tomcat-3.0.2.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-core\10.1.5\tomcat-embed-core-10.1.5.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.5\tomcat-embed-websocket-10.1.5.jar;D:\Develop\maven\local\org\springframework\spring-web\6.0.4\spring-web-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-beans\6.0.4\spring-beans-6.0.4.jar;D:\Develop\maven\local\io\micrometer\micrometer-observation\1.10.3\micrometer-observation-1.10.3.jar;D:\Develop\maven\local\io\micrometer\micrometer-commons\1.10.3\micrometer-commons-1.10.3.jar;D:\Develop\maven\local\org\springframework\spring-webmvc\6.0.4\spring-webmvc-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-context\6.0.4\spring-context-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-expression\6.0.4\spring-expression-6.0.4.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2022.0.0.0\spring-cloud-starter-alibaba-nacos-discovery-2022.0.0.0.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-alibaba-commons\2022.0.0.0\spring-cloud-alibaba-commons-2022.0.0.0.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-client\2.2.1\nacos-client-2.2.1.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-auth-plugin\2.2.1\nacos-auth-plugin-2.2.1.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-encryption-plugin\2.2.1\nacos-encryption-plugin-2.2.1.jar;D:\Develop\maven\local\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-core\2.14.1\jackson-core-2.14.1.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\Develop\maven\local\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\Develop\maven\local\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;D:\Develop\maven\local\org\springframework\cloud\spring-cloud-commons\4.0.0\spring-cloud-commons-4.0.0.jar;D:\Develop\maven\local\org\springframework\security\spring-security-crypto\6.0.1\spring-security-crypto-6.0.1.jar;D:\Develop\maven\local\org\springframework\cloud\spring-cloud-context\4.0.0\spring-cloud-context-4.0.0.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2022.0.0.0\spring-cloud-starter-alibaba-nacos-config-2022.0.0.0.jar;D:\Develop\maven\local\org\slf4j\slf4j-api\2.0.6\slf4j-api-2.0.6.jar;D:\Develop\maven\local\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-boot-starter\3.5.7\mybatis-plus-boot-starter-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus\3.5.7\mybatis-plus-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-core\3.5.7\mybatis-plus-core-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-annotation\3.5.7\mybatis-plus-annotation-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-extension\3.5.7\mybatis-plus-extension-3.5.7.jar;D:\Develop\maven\local\org\mybatis\mybatis\3.5.16\mybatis-3.5.16.jar;D:\Develop\maven\local\com\github\jsqlparser\jsqlparser\4.9\jsqlparser-4.9.jar;D:\Develop\maven\local\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.7\mybatis-plus-spring-boot-autoconfigure-3.5.7.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-autoconfigure\3.0.2\spring-boot-autoconfigure-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-jdbc\3.0.2\spring-boot-starter-jdbc-3.0.2.jar;D:\Develop\maven\local\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Develop\maven\local\org\springframework\spring-jdbc\6.0.4\spring-jdbc-6.0.4.jar;D:\Develop\maven\local\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\Develop\maven\local\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;D:\Develop\maven\local\com\alibaba\druid-spring-boot-starter\1.2.22\druid-spring-boot-starter-1.2.22.jar;D:\Develop\maven\local\com\alibaba\druid\1.2.22\druid-1.2.22.jar;D:\Develop\maven\local\org\glassfish\jaxb\jaxb-runtime\4.0.1\jaxb-runtime-4.0.1.jar;D:\Develop\maven\local\org\glassfish\jaxb\jaxb-core\4.0.1\jaxb-core-4.0.1.jar;D:\Develop\maven\local\org\eclipse\angus\angus-activation\1.0.0\angus-activation-1.0.0.jar;D:\Develop\maven\local\org\glassfish\jaxb\txw2\4.0.1\txw2-4.0.1.jar;D:\Develop\maven\local\com\sun\istack\istack-commons-runtime\4.1.1\istack-commons-runtime-4.1.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-data-redis\3.0.2\spring-boot-starter-data-redis-3.0.2.jar;D:\Develop\maven\local\org\springframework\data\spring-data-redis\3.0.1\spring-data-redis-3.0.1.jar;D:\Develop\maven\local\org\springframework\data\spring-data-keyvalue\3.0.1\spring-data-keyvalue-3.0.1.jar;D:\Develop\maven\local\org\springframework\data\spring-data-commons\3.0.1\spring-data-commons-3.0.1.jar;D:\Develop\maven\local\org\springframework\spring-tx\6.0.4\spring-tx-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-oxm\6.0.4\spring-oxm-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-context-support\6.0.4\spring-context-support-6.0.4.jar;D:\Develop\maven\local\io\lettuce\lettuce-core\6.2.2.RELEASE\lettuce-core-6.2.2.RELEASE.jar;D:\Develop\maven\local\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;D:\Develop\maven\local\io\projectreactor\reactor-core\3.5.2\reactor-core-3.5.2.jar;D:\Develop\maven\local\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-security\3.0.2\spring-boot-starter-security-3.0.2.jar;D:\Develop\maven\local\org\springframework\spring-aop\6.0.4\spring-aop-6.0.4.jar;D:\Develop\maven\local\org\springframework\security\spring-security-config\6.0.1\spring-security-config-6.0.1.jar;D:\Develop\maven\local\org\springframework\security\spring-security-core\6.0.1\spring-security-core-6.0.1.jar;D:\Develop\maven\local\org\springframework\security\spring-security-web\6.0.1\spring-security-web-6.0.1.jar;C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\common\target\common-1.0.0.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\Develop\maven\local\org\projectlombok\lombok\1.18.32\lombok-1.18.32.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-validation\3.0.2\spring-boot-starter-validation-3.0.2.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-el\10.1.5\tomcat-embed-el-10.1.5.jar;D:\Develop\maven\local\org\hibernate\validator\hibernate-validator\8.0.0.Final\hibernate-validator-8.0.0.Final.jar;D:\Develop\maven\local\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Develop\maven\local\org\jboss\logging\jboss-logging\3.5.0.Final\jboss-logging-3.5.0.Final.jar;D:\Develop\maven\local\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-test\3.0.2\spring-boot-starter-test-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-test\3.0.2\spring-boot-test-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-test-autoconfigure\3.0.2\spring-boot-test-autoconfigure-3.0.2.jar;D:\Develop\maven\local\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\Develop\maven\local\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\Develop\maven\local\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\Develop\maven\local\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\Develop\maven\local\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;D:\Develop\maven\local\jakarta\activation\jakarta.activation-api\2.1.1\jakarta.activation-api-2.1.1.jar;D:\Develop\maven\local\org\assertj\assertj-core\3.23.1\assertj-core-3.23.1.jar;D:\Develop\maven\local\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;D:\Develop\maven\local\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;D:\Develop\maven\local\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Develop\maven\local\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;D:\Develop\maven\local\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;D:\Develop\maven\local\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;D:\Develop\maven\local\org\mockito\mockito-core\4.8.1\mockito-core-4.8.1.jar;D:\Develop\maven\local\net\bytebuddy\byte-buddy-agent\1.12.22\byte-buddy-agent-1.12.22.jar;D:\Develop\maven\local\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Develop\maven\local\org\mockito\mockito-junit-jupiter\4.8.1\mockito-junit-jupiter-4.8.1.jar;D:\Develop\maven\local\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Develop\maven\local\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Develop\maven\local\org\springframework\spring-core\6.0.4\spring-core-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-jcl\6.0.4\spring-jcl-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-test\6.0.4\spring-test-6.0.4.jar;D:\Develop\maven\local\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Develop\maven\local\com\h2database\h2\2.1.214\h2-2.1.214.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Develop\JDKS\jdk- ********-hotspot\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire5150453766173157883\surefirebooter-20250711183212354_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire5150453766173157883 2025-07-11T18-32-11_313-jvmRun1 surefire-20250711183212354_1tmp surefire_0-20250711183212354_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\test-classes;C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-web\3.0.2\spring-boot-starter-web-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter\3.0.2\spring-boot-starter-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot\3.0.2\spring-boot-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-logging\3.0.2\spring-boot-starter-logging-3.0.2.jar;D:\Develop\maven\local\ch\qos\logback\logback-classic\1.4.5\logback-classic-1.4.5.jar;D:\Develop\maven\local\ch\qos\logback\logback-core\1.4.5\logback-core-1.4.5.jar;D:\Develop\maven\local\org\apache\logging\log4j\log4j-to-slf4j\2.19.0\log4j-to-slf4j-2.19.0.jar;D:\Develop\maven\local\org\apache\logging\log4j\log4j-api\2.19.0\log4j-api-2.19.0.jar;D:\Develop\maven\local\org\slf4j\jul-to-slf4j\2.0.6\jul-to-slf4j-2.0.6.jar;D:\Develop\maven\local\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-json\3.0.2\spring-boot-starter-json-3.0.2.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-databind\2.14.1\jackson-databind-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-annotations\2.14.1\jackson-annotations-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.14.1\jackson-datatype-jdk8-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.14.1\jackson-datatype-jsr310-2.14.1.jar;D:\Develop\maven\local\com\fasterxml\jackson\module\jackson-module-parameter-names\2.14.1\jackson-module-parameter-names-2.14.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-tomcat\3.0.2\spring-boot-starter-tomcat-3.0.2.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-core\10.1.5\tomcat-embed-core-10.1.5.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.5\tomcat-embed-websocket-10.1.5.jar;D:\Develop\maven\local\org\springframework\spring-web\6.0.4\spring-web-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-beans\6.0.4\spring-beans-6.0.4.jar;D:\Develop\maven\local\io\micrometer\micrometer-observation\1.10.3\micrometer-observation-1.10.3.jar;D:\Develop\maven\local\io\micrometer\micrometer-commons\1.10.3\micrometer-commons-1.10.3.jar;D:\Develop\maven\local\org\springframework\spring-webmvc\6.0.4\spring-webmvc-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-context\6.0.4\spring-context-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-expression\6.0.4\spring-expression-6.0.4.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2022.0.0.0\spring-cloud-starter-alibaba-nacos-discovery-2022.0.0.0.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-alibaba-commons\2022.0.0.0\spring-cloud-alibaba-commons-2022.0.0.0.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-client\2.2.1\nacos-client-2.2.1.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-auth-plugin\2.2.1\nacos-auth-plugin-2.2.1.jar;D:\Develop\maven\local\com\alibaba\nacos\nacos-encryption-plugin\2.2.1\nacos-encryption-plugin-2.2.1.jar;D:\Develop\maven\local\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;D:\Develop\maven\local\com\fasterxml\jackson\core\jackson-core\2.14.1\jackson-core-2.14.1.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;D:\Develop\maven\local\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;D:\Develop\maven\local\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;D:\Develop\maven\local\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;D:\Develop\maven\local\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;D:\Develop\maven\local\org\springframework\cloud\spring-cloud-commons\4.0.0\spring-cloud-commons-4.0.0.jar;D:\Develop\maven\local\org\springframework\security\spring-security-crypto\6.0.1\spring-security-crypto-6.0.1.jar;D:\Develop\maven\local\org\springframework\cloud\spring-cloud-context\4.0.0\spring-cloud-context-4.0.0.jar;D:\Develop\maven\local\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2022.0.0.0\spring-cloud-starter-alibaba-nacos-config-2022.0.0.0.jar;D:\Develop\maven\local\org\slf4j\slf4j-api\2.0.6\slf4j-api-2.0.6.jar;D:\Develop\maven\local\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-boot-starter\3.5.7\mybatis-plus-boot-starter-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus\3.5.7\mybatis-plus-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-core\3.5.7\mybatis-plus-core-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-annotation\3.5.7\mybatis-plus-annotation-3.5.7.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-extension\3.5.7\mybatis-plus-extension-3.5.7.jar;D:\Develop\maven\local\org\mybatis\mybatis\3.5.16\mybatis-3.5.16.jar;D:\Develop\maven\local\com\github\jsqlparser\jsqlparser\4.9\jsqlparser-4.9.jar;D:\Develop\maven\local\org\mybatis\mybatis-spring\2.1.2\mybatis-spring-2.1.2.jar;D:\Develop\maven\local\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.7\mybatis-plus-spring-boot-autoconfigure-3.5.7.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-autoconfigure\3.0.2\spring-boot-autoconfigure-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-jdbc\3.0.2\spring-boot-starter-jdbc-3.0.2.jar;D:\Develop\maven\local\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Develop\maven\local\org\springframework\spring-jdbc\6.0.4\spring-jdbc-6.0.4.jar;D:\Develop\maven\local\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\Develop\maven\local\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;D:\Develop\maven\local\com\alibaba\druid-spring-boot-starter\1.2.22\druid-spring-boot-starter-1.2.22.jar;D:\Develop\maven\local\com\alibaba\druid\1.2.22\druid-1.2.22.jar;D:\Develop\maven\local\org\glassfish\jaxb\jaxb-runtime\4.0.1\jaxb-runtime-4.0.1.jar;D:\Develop\maven\local\org\glassfish\jaxb\jaxb-core\4.0.1\jaxb-core-4.0.1.jar;D:\Develop\maven\local\org\eclipse\angus\angus-activation\1.0.0\angus-activation-1.0.0.jar;D:\Develop\maven\local\org\glassfish\jaxb\txw2\4.0.1\txw2-4.0.1.jar;D:\Develop\maven\local\com\sun\istack\istack-commons-runtime\4.1.1\istack-commons-runtime-4.1.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-data-redis\3.0.2\spring-boot-starter-data-redis-3.0.2.jar;D:\Develop\maven\local\org\springframework\data\spring-data-redis\3.0.1\spring-data-redis-3.0.1.jar;D:\Develop\maven\local\org\springframework\data\spring-data-keyvalue\3.0.1\spring-data-keyvalue-3.0.1.jar;D:\Develop\maven\local\org\springframework\data\spring-data-commons\3.0.1\spring-data-commons-3.0.1.jar;D:\Develop\maven\local\org\springframework\spring-tx\6.0.4\spring-tx-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-oxm\6.0.4\spring-oxm-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-context-support\6.0.4\spring-context-support-6.0.4.jar;D:\Develop\maven\local\io\lettuce\lettuce-core\6.2.2.RELEASE\lettuce-core-6.2.2.RELEASE.jar;D:\Develop\maven\local\io\netty\netty-common\4.1.87.Final\netty-common-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-handler\4.1.87.Final\netty-handler-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-resolver\4.1.87.Final\netty-resolver-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-buffer\4.1.87.Final\netty-buffer-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-transport-native-unix-common\4.1.87.Final\netty-transport-native-unix-common-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-codec\4.1.87.Final\netty-codec-4.1.87.Final.jar;D:\Develop\maven\local\io\netty\netty-transport\4.1.87.Final\netty-transport-4.1.87.Final.jar;D:\Develop\maven\local\io\projectreactor\reactor-core\3.5.2\reactor-core-3.5.2.jar;D:\Develop\maven\local\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-security\3.0.2\spring-boot-starter-security-3.0.2.jar;D:\Develop\maven\local\org\springframework\spring-aop\6.0.4\spring-aop-6.0.4.jar;D:\Develop\maven\local\org\springframework\security\spring-security-config\6.0.1\spring-security-config-6.0.1.jar;D:\Develop\maven\local\org\springframework\security\spring-security-core\6.0.1\spring-security-core-6.0.1.jar;D:\Develop\maven\local\org\springframework\security\spring-security-web\6.0.1\spring-security-web-6.0.1.jar;C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\common\target\common-1.0.0.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\Develop\maven\local\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\Develop\maven\local\org\projectlombok\lombok\1.18.32\lombok-1.18.32.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-validation\3.0.2\spring-boot-starter-validation-3.0.2.jar;D:\Develop\maven\local\org\apache\tomcat\embed\tomcat-embed-el\10.1.5\tomcat-embed-el-10.1.5.jar;D:\Develop\maven\local\org\hibernate\validator\hibernate-validator\8.0.0.Final\hibernate-validator-8.0.0.Final.jar;D:\Develop\maven\local\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Develop\maven\local\org\jboss\logging\jboss-logging\3.5.0.Final\jboss-logging-3.5.0.Final.jar;D:\Develop\maven\local\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-starter-test\3.0.2\spring-boot-starter-test-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-test\3.0.2\spring-boot-test-3.0.2.jar;D:\Develop\maven\local\org\springframework\boot\spring-boot-test-autoconfigure\3.0.2\spring-boot-test-autoconfigure-3.0.2.jar;D:\Develop\maven\local\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\Develop\maven\local\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\Develop\maven\local\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\Develop\maven\local\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\Develop\maven\local\jakarta\xml\bind\jakarta.xml.bind-api\4.0.0\jakarta.xml.bind-api-4.0.0.jar;D:\Develop\maven\local\jakarta\activation\jakarta.activation-api\2.1.1\jakarta.activation-api-2.1.1.jar;D:\Develop\maven\local\org\assertj\assertj-core\3.23.1\assertj-core-3.23.1.jar;D:\Develop\maven\local\net\bytebuddy\byte-buddy\1.12.22\byte-buddy-1.12.22.jar;D:\Develop\maven\local\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter\5.9.2\junit-jupiter-5.9.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-api\5.9.2\junit-jupiter-api-5.9.2.jar;D:\Develop\maven\local\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Develop\maven\local\org\junit\platform\junit-platform-commons\1.9.2\junit-platform-commons-1.9.2.jar;D:\Develop\maven\local\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-params\5.9.2\junit-jupiter-params-5.9.2.jar;D:\Develop\maven\local\org\junit\jupiter\junit-jupiter-engine\5.9.2\junit-jupiter-engine-5.9.2.jar;D:\Develop\maven\local\org\junit\platform\junit-platform-engine\1.9.2\junit-platform-engine-1.9.2.jar;D:\Develop\maven\local\org\mockito\mockito-core\4.8.1\mockito-core-4.8.1.jar;D:\Develop\maven\local\net\bytebuddy\byte-buddy-agent\1.12.22\byte-buddy-agent-1.12.22.jar;D:\Develop\maven\local\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Develop\maven\local\org\mockito\mockito-junit-jupiter\4.8.1\mockito-junit-jupiter-4.8.1.jar;D:\Develop\maven\local\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Develop\maven\local\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Develop\maven\local\org\springframework\spring-core\6.0.4\spring-core-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-jcl\6.0.4\spring-jcl-6.0.4.jar;D:\Develop\maven\local\org\springframework\spring-test\6.0.4\spring-test-6.0.4.jar;D:\Develop\maven\local\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\Develop\maven\local\com\h2database\h2\2.1.214\h2-2.1.214.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Develop\JDKS\jdk- ********-hotspot"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire5150453766173157883\surefirebooter-20250711183212354_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+6-LTS"/>
    <property name="user.name" value="86188"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-21.0.7+6"/>
    <property name="localRepository" value="D:\Develop\maven\local"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2024.1.3"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="33364"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Develop\JDKS\jdk- ********-hotspot\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\Develop\JDKS\jdk- ********-hotspot\bin;C:\Program Files (x86)\VMware\VMware Workstation\bin\;D:\Develop\MYSQL\mysql-8.0.31-winx64\bin;D:\Develop\jdk\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\dotnet\;C:\Program Files\Bandizip\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Develop\maven\apache-maven-3.9.8-bin\apache-maven-3.9.8\bin;D:\Develop\JDKS\jdk-22_windows-x64_bin\jdk-22.0.1\bin;;D:\Develop\npm_dowwnload\node_global;D:\Develop\Git\cmd;C:\Program Files (x86)\NetSarang\Xftp 7\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\nodejs\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\WingetUI\choco-cli\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Develop\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;D:\Develop\IntelliJ IDEA 2024.1.3\bin;;;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="21.0.7+6-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="maven.repo.local" value="D:\Develop\maven\local"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="contextLoads" classname="com.healthdiet.auth.AuthApplicationTest" time="0.007">
    <error message="Failed to load ApplicationContext for [WebMergedContextConfiguration@178dc733 testClass = com.healthdiet.auth.AuthApplicationTest, locations = [], classes = [com.healthdiet.auth.AuthApplication], contextInitializerClasses = [], activeProfiles = [&quot;test&quot;], propertySourceLocations = [], propertySourceProperties = [&quot;org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true&quot;], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@37313c65, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@7486b455, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@311bf055, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@9da1, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@6bb4dd34, org.springframework.boot.test.context.SpringBootTestAnnotation@28000246], resourceBasePath = &quot;src/main/webapp&quot;, contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]" type="java.lang.IllegalStateException"><![CDATA[java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@178dc733 testClass = com.healthdiet.auth.AuthApplicationTest, locations = [], classes = [com.healthdiet.auth.AuthApplication], contextInitializerClasses = [], activeProfiles = ["test"], propertySourceLocations = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@37313c65, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@7486b455, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@311bf055, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@9da1, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@6bb4dd34, org.springframework.boot.test.context.SpringBootTestAnnotation@28000246], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:142)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:127)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:191)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:130)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:241)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$10(ClassBasedTestDescriptor.java:377)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:382)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$11(ClassBasedTestDescriptor.java:377)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:376)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:289)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:288)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:278)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:277)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:105)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:104)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:68)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:147)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:127)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:90)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:55)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:102)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'authServiceImpl' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\service\impl\AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:245)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1344)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:961)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:915)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:584)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:730)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:432)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:59)
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:47)
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1386)
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:543)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:184)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:118)
	... 73 more
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\service\impl\AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:245)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1344)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1405)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1325)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 97 more
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:1812)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1371)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1325)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 111 more
]]></error>
    <system-out><![CDATA[18:32:13.137 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [AuthApplicationTest]: using SpringBootContextLoader
18:32:13.140 [main] DEBUG org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.healthdiet.auth.AuthApplicationTest]: no resource found for suffixes {-context.xml, Context.groovy}.
18:32:13.140 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.healthdiet.auth.AuthApplicationTest]: AuthApplicationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
18:32:13.160 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using ContextCustomizers for test class [AuthApplicationTest]: [ExcludeFilterContextCustomizer, DuplicateJsonObjectContextCustomizer, MockitoContextCustomizer, TestRestTemplateContextCustomizer, DisableObservabilityContextCustomizer, PropertyMappingContextCustomizer, Customizer]
18:32:13.216 [main] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\AuthApplication.class]
18:32:13.217 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.healthdiet.auth.AuthApplication for test class com.healthdiet.auth.AuthApplicationTest
18:32:13.291 [main] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners for test class [AuthApplicationTest]: [ServletTestExecutionListener, DirtiesContextBeforeModesTestExecutionListener, ApplicationEventsTestExecutionListener, MockitoTestExecutionListener, DependencyInjectionTestExecutionListener, DirtiesContextTestExecutionListener, TransactionalTestExecutionListener, SqlScriptsTestExecutionListener, EventPublishingTestExecutionListener, ResetMocksTestExecutionListener, RestDocsTestExecutionListener, MockRestServiceServerResetTestExecutionListener, MockMvcPrintOnlyOnFailureTestExecutionListener, WebDriverTestExecutionListener, MockWebServiceServerTestExecutionListener]
18:32:13.292 [main] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: class [AuthApplicationTest], class annotated with @DirtiesContext [false] with mode [null]
2025-07-11T18:32:14.463+08:00  WARN 33364 --- [           main] c.a.nacos.client.logging.NacosLogging    : Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.0.2)

2025-07-11T18:32:14.501+08:00  WARN 33364 --- [           main] c.a.nacos.client.logging.NacosLogging    : Load Logback Configuration of Nacos fail, message: Could not initialize Logback Nacos logging from classpath:nacos-logback.xml
2025-07-11T18:32:14.503+08:00  INFO 33364 --- [           main] c.healthdiet.auth.AuthApplicationTest    : Starting AuthApplicationTest using Java 21.0.7 with PID 33364 (started by 86188 in C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service)
2025-07-11T18:32:14.504+08:00 DEBUG 33364 --- [           main] c.healthdiet.auth.AuthApplicationTest    : Running with Spring Boot v3.0.2, Spring v6.0.4
2025-07-11T18:32:14.504+08:00  INFO 33364 --- [           main] c.healthdiet.auth.AuthApplicationTest    : The following 1 profile is active: "test"
2025-07-11T18:32:14.528+08:00  INFO 33364 --- [           main] c.a.c.n.c.NacosConfigDataLoader          : [Nacos Config] Load config[dataId=auth-service.yml, group=DEFAULT_GROUP] success
2025-07-11T18:32:15.118+08:00  INFO 33364 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-11T18:32:15.123+08:00  INFO 33364 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-11T18:32:15.148+08:00  INFO 33364 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-07-11T18:32:15.322+08:00  INFO 33364 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=e7d338d2-e9c3-33db-816c-d66ae5cd9236
2025-07-11T18:32:15.667+08:00 DEBUG 33364 --- [           main] c.b.m.e.s.MybatisSqlSessionFactoryBean   : Property 'mapperLocations' was not specified.
2025-07-11T18:32:15.672+08:00 DEBUG 33364 --- [           main] c.b.mybatisplus.core.toolkit.Sequence    : Get /*********** network interface 
2025-07-11T18:32:15.672+08:00 DEBUG 33364 --- [           main] c.b.mybatisplus.core.toolkit.Sequence    : Get network interface info: name:wireless_32768 (Intel(R) Wi-Fi 6 AX201 160MHz)
2025-07-11T18:32:15.675+08:00 DEBUG 33364 --- [           main] c.b.mybatisplus.core.toolkit.Sequence    : Initialization Sequence datacenterId:17 workerId:11
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.7 
2025-07-11T18:32:15.817+08:00  WARN 33364 --- [           main] o.s.w.c.s.GenericWebApplicationContext   : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'authServiceImpl' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\service\impl\AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-07-11T18:32:15.818+08:00  INFO 33364 --- [           main] com.alibaba.druid.pool.DruidDataSource   : {dataSource-0} closing ...
2025-07-11T18:32:15.825+08:00  INFO 33364 --- [           main] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-11T18:32:15.846+08:00 ERROR 33364 --- [           main] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 2 of constructor in com.healthdiet.auth.service.impl.AuthServiceImpl required a bean of type 'com.healthdiet.common.util.JwtUtil' that could not be found.


Action:

Consider defining a bean of type 'com.healthdiet.common.util.JwtUtil' in your configuration.

2025-07-11T18:32:15.853+08:00 ERROR 33364 --- [           main] o.s.test.context.TestContextManager      : Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener] to prepare test instance [com.healthdiet.auth.AuthApplicationTest@4641f66c]

java.lang.IllegalStateException: Failed to load ApplicationContext for [WebMergedContextConfiguration@178dc733 testClass = com.healthdiet.auth.AuthApplicationTest, locations = [], classes = [com.healthdiet.auth.AuthApplication], contextInitializerClasses = [], activeProfiles = ["test"], propertySourceLocations = [], propertySourceProperties = ["org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true"], contextCustomizers = [org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@37313c65, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@7486b455, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@311bf055, org.springframework.boot.test.autoconfigure.actuate.observability.ObservabilityContextCustomizerFactory$DisableObservabilityContextCustomizer@9da1, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@6bb4dd34, org.springframework.boot.test.context.SpringBootTestAnnotation@28000246], resourceBasePath = "src/main/webapp", contextLoader = org.springframework.boot.test.context.SpringBootContextLoader, parent = null]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:142) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:127) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:191) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:130) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:241) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138) ~[spring-test-6.0.4.jar:6.0.4]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$10(ClassBasedTestDescriptor.java:377) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:382) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$11(ClassBasedTestDescriptor.java:377) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179) ~[na:na]
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1708) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310) ~[na:na]
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735) ~[na:na]
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762) ~[na:na]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:376) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:289) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:288) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:278) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at java.base/java.util.Optional.orElseGet(Optional.java:364) ~[na:na]
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:277) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:105) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:104) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:68) ~[junit-jupiter-engine-5.9.2.jar:5.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596) ~[na:na]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596) ~[na:na]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54) ~[junit-platform-engine-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:147) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:127) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:90) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:55) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:102) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:54) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86) ~[junit-platform-launcher-1.9.2.jar:1.9.2]
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56) ~[surefire-junit-platform-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184) ~[surefire-junit-platform-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148) ~[surefire-junit-platform-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122) ~[surefire-junit-platform-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385) ~[surefire-booter-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162) ~[surefire-booter-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507) ~[surefire-booter-3.2.5.jar:3.2.5]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495) ~[surefire-booter-3.2.5.jar:3.2.5]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'authServiceImpl' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\service\impl\AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:245) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1344) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1188) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:961) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:915) ~[spring-context-6.0.4.jar:6.0.4]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:584) ~[spring-context-6.0.4.jar:6.0.4]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:730) ~[spring-boot-3.0.2.jar:3.0.2]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:432) ~[spring-boot-3.0.2.jar:3.0.2]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) ~[spring-boot-3.0.2.jar:3.0.2]
	at org.springframework.boot.test.context.SpringBootContextLoader.lambda$loadContext$3(SpringBootContextLoader.java:137) ~[spring-boot-test-3.0.2.jar:3.0.2]
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:59) ~[spring-core-6.0.4.jar:6.0.4]
	at org.springframework.util.function.ThrowingSupplier.get(ThrowingSupplier.java:47) ~[spring-core-6.0.4.jar:6.0.4]
	at org.springframework.boot.SpringApplication.withHook(SpringApplication.java:1386) ~[spring-boot-3.0.2.jar:3.0.2]
	at org.springframework.boot.test.context.SpringBootContextLoader$ContextLoaderHook.run(SpringBootContextLoader.java:543) ~[spring-boot-test-3.0.2.jar:3.0.2]
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:137) ~[spring-boot-test-3.0.2.jar:3.0.2]
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:108) ~[spring-boot-test-3.0.2.jar:3.0.2]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:184) ~[spring-test-6.0.4.jar:6.0.4]
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:118) ~[spring-test-6.0.4.jar:6.0.4]
	... 73 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authServiceImpl' defined in file [C:\Users\<USER>\Desktop\idea\healthy-diet-platform\health-backend\auth-service\target\classes\com\healthdiet\auth\service\impl\AuthServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:245) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1344) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1188) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:561) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:521) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1405) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1325) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789) ~[spring-beans-6.0.4.jar:6.0.4]
	... 97 common frames omitted
Caused by: org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.healthdiet.common.util.JwtUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.raiseNoMatchingBeanFound(DefaultListableBeanFactory.java:1812) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1371) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1325) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885) ~[spring-beans-6.0.4.jar:6.0.4]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789) ~[spring-beans-6.0.4.jar:6.0.4]
	... 111 common frames omitted

]]></system-out>
    <system-err><![CDATA[


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

    None


Negative matches:
-----------------

    None


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    None



]]></system-err>
  </testcase>
</testsuite>