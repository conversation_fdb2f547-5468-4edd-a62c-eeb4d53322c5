# Docker环境部署指南

## 概述

本项目已配置为使用Docker容器化部署，包括：
- MySQL 8.0 数据库
- Redis 7 缓存
- RabbitMQ 3.12 消息队列
- Nacos 2.3.0 注册中心和配置中心

## 快速启动

### 1. 启动Docker服务

```bash
# Windows
start-docker-services.bat

# Linux/Mac
docker-compose up -d
```

### 2. 验证服务状态

```bash
docker-compose ps
```

### 3. 访问管理界面

- **Nacos管理界面**: http://localhost:8848/nacos
  - 用户名/密码: `nacos/nacos`
  
- **RabbitMQ管理界面**: http://localhost:15672
  - 用户名/密码: `guest/guest`

### 4. 启动后端服务

等待Docker服务完全启动后（约30秒），运行：

```bash
# Windows
cd health-backend
start-services.bat

# 或者单独启动各个服务
start-auth-service.bat
```

## 服务端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| MySQL | 3306 | 3307 | 数据库 |
| Redis | 6379 | 6380 | 缓存 |
| RabbitMQ | 5672/15672 | 5672/15672 | 消息队列 |
| Nacos | 8848/9848 | 8848/9848 | 注册中心 |

## 配置说明

### Nacos配置

所有后端服务已配置为连接Docker中的Nacos：
- 服务发现地址: `nacos:8848`
- 配置中心地址: `nacos:8848`

### 数据库配置

- 主数据库: `healthy_diet_platform`
- Nacos配置数据库: `nacos_config`
- 用户名: `root`
- 密码: `mysql123`

## 故障排除

### 1. 服务启动失败

```bash
# 查看服务日志
docker-compose logs nacos
docker-compose logs mysql
```

### 2. 重新启动服务

```bash
# 停止所有服务
docker-compose down

# 清理数据卷（谨慎使用）
docker-compose down -v

# 重新启动
docker-compose up -d
```

### 3. Nacos连接问题

确保：
1. Nacos容器已完全启动
2. MySQL容器正常运行
3. 后端服务配置正确

## 开发环境切换

### 从本地Nacos切换到Docker Nacos

1. 停止本地Nacos服务
2. 启动Docker服务: `start-docker-services.bat`
3. 启动后端服务

### 从Docker Nacos切换回本地Nacos

1. 停止Docker服务: `stop-docker-services.bat`
2. 修改各服务配置文件中的Nacos地址为 `localhost:8848`
3. 启动本地Nacos服务
4. 启动后端服务

## 注意事项

1. **首次启动**: Docker镜像下载可能需要较长时间
2. **数据持久化**: 数据存储在Docker卷中，停止容器不会丢失数据
3. **端口冲突**: 确保主机端口未被其他服务占用
4. **内存要求**: 建议至少4GB可用内存
