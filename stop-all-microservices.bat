@echo off
chcp 65001 >nul
echo ========================================
echo 停止健康饮食平台所有微服务
echo ========================================

echo.
echo 正在查找并停止所有Java微服务进程...

echo.
echo 停止 Gateway Service (端口: 8080)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Gateway Service 已停止
)

echo 停止 Auth Service (端口: 8081)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Auth Service 已停止
)

echo 停止 User Service (端口: 8082)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8082') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo User Service 已停止
)

echo 停止 Recipe Service (端口: 8083)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8083') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Recipe Service 已停止
)

echo 停止 Plan Service (端口: 8084)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8084') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Plan Service 已停止
)

echo 停止 Admin Service (端口: 8085)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8085') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Admin Service 已停止
)

echo 停止 AIGC Service (端口: 8086)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8086') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo AIGC Service 已停止
)

echo.
echo 关闭所有Maven和Java相关的命令行窗口...
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Gateway Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Auth Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq User Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Recipe Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Plan Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Admin Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq AIGC Service*" >nul 2>&1

echo.
echo ========================================
echo 所有微服务已停止
echo ========================================
echo.
echo 注意: Docker基础设施服务仍在运行
echo 如需停止Docker服务，请运行: docker-compose down
echo.

pause
