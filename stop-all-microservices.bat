@echo off
echo ========================================
echo Stopping All Microservices
echo ========================================

echo.
echo Finding and stopping all Java microservice processes...

echo.
echo Stopping Gateway Service (Port: 8080)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Gateway Service stopped
)

echo Stopping Auth Service (Port: 8081)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Auth Service stopped
)

echo Stopping User Service (Port: 8082)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8082') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo User Service stopped
)

echo Stopping Recipe Service (Port: 8083)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8083') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Recipe Service stopped
)

echo Stopping Plan Service (Port: 8084)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8084') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Plan Service stopped
)

echo Stopping Admin Service (Port: 8085)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8085') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo Admin Service stopped
)

echo Stopping AIGC Service (Port: 8086)...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8086') do (
    taskkill /PID %%a /F >nul 2>&1
    if not errorlevel 1 echo AIGC Service stopped
)

echo.
echo Closing all Maven and Java related command windows...
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Gateway Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Auth Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq User Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Recipe Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Plan Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq Admin Service*" >nul 2>&1
taskkill /F /IM cmd.exe /FI "WINDOWTITLE eq AIGC Service*" >nul 2>&1

echo.
echo ========================================
echo All microservices stopped
echo ========================================
echo.
echo Note: Docker infrastructure services are still running
echo To stop Docker services, run: docker-compose down
echo.

pause
