package com.healthdiet.common.config; // 或者你喜欢的任何包名

import com.healthdiet.common.util.JwtUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration // 声明这是一个配置类
public class CommonAutoConfiguration {

    @Bean // 告诉Spring，这个方法返回的对象要作为一个Bean被管理
    public JwtUtil jwtUtil() {
        return new JwtUtil();
        // 如果你的JwtUtil构造函数需要参数（比如从配置文件读取的密钥），
        // 你可以在这里注入它们，例如：
        // @Value("${jwt.secret}") private String secret;
        // return new JwtUtil(secret);
    }

    // 你还可以在这里定义其他所有需要在各个微服务中共享的Bean
}