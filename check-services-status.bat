@echo off
chcp 65001 >nul
echo ========================================
echo 健康饮食平台服务状态检查
echo ========================================

echo.
echo [1] Docker基础设施服务状态:
echo ========================================
docker-compose ps

echo.
echo [2] 微服务端口占用检查:
echo ========================================

echo 检查 Gateway Service (8080):
netstat -ano | findstr :8080 >nul
if %errorlevel% equ 0 (
    echo ✅ Gateway Service - 运行中
) else (
    echo ❌ Gateway Service - 未运行
)

echo 检查 Auth Service (8081):
netstat -ano | findstr :8081 >nul
if %errorlevel% equ 0 (
    echo ✅ Auth Service - 运行中
) else (
    echo ❌ Auth Service - 未运行
)

echo 检查 User Service (8082):
netstat -ano | findstr :8082 >nul
if %errorlevel% equ 0 (
    echo ✅ User Service - 运行中
) else (
    echo ❌ User Service - 未运行
)

echo 检查 Recipe Service (8083):
netstat -ano | findstr :8083 >nul
if %errorlevel% equ 0 (
    echo ✅ Recipe Service - 运行中
) else (
    echo ❌ Recipe Service - 未运行
)

echo 检查 Plan Service (8084):
netstat -ano | findstr :8084 >nul
if %errorlevel% equ 0 (
    echo ✅ Plan Service - 运行中
) else (
    echo ❌ Plan Service - 未运行
)

echo 检查 Admin Service (8085):
netstat -ano | findstr :8085 >nul
if %errorlevel% equ 0 (
    echo ✅ Admin Service - 运行中
) else (
    echo ❌ Admin Service - 未运行
)

echo 检查 AIGC Service (8086):
netstat -ano | findstr :8086 >nul
if %errorlevel% equ 0 (
    echo ✅ AIGC Service - 运行中
) else (
    echo ❌ AIGC Service - 未运行
)

echo.
echo [3] API健康检查:
echo ========================================

echo 测试 Gateway Service API:
curl -s http://localhost:8080/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Gateway API - 响应正常
) else (
    echo ❌ Gateway API - 无响应
)

echo 测试 User Service API:
curl -s http://localhost:8082/health-profile/1 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ User API - 响应正常
) else (
    echo ❌ User API - 无响应
)

echo 测试 Recipe Service API:
curl -s http://localhost:8083/recipes >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Recipe API - 响应正常
) else (
    echo ❌ Recipe API - 无响应
)

echo 测试 AIGC Service API:
curl -s http://localhost:8086/aigc/task/test >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ AIGC API - 响应正常
) else (
    echo ❌ AIGC API - 无响应
)

echo.
echo [4] 快速访问链接:
echo ========================================
echo Nacos控制台: http://localhost:8848/nacos
echo Gateway入口: http://localhost:8080
echo.

pause
