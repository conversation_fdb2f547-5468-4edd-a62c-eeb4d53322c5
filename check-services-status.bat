@echo off
echo ========================================
echo Service Status Check
echo ========================================

echo.
echo [1] Docker Infrastructure Service Status:
echo ========================================
docker-compose ps

echo.
echo [2] Microservice Port Check:
echo ========================================

echo Checking Gateway Service (8080):
netstat -ano | findstr :8080 >nul
if %errorlevel% equ 0 (
    echo [OK] Gateway Service - Running
) else (
    echo [X] Gateway Service - Not Running
)

echo Checking Auth Service (8081):
netstat -ano | findstr :8081 >nul
if %errorlevel% equ 0 (
    echo [OK] Auth Service - Running
) else (
    echo [X] Auth Service - Not Running
)

echo Checking User Service (8082):
netstat -ano | findstr :8082 >nul
if %errorlevel% equ 0 (
    echo [OK] User Service - Running
) else (
    echo [X] User Service - Not Running
)

echo Checking Recipe Service (8083):
netstat -ano | findstr :8083 >nul
if %errorlevel% equ 0 (
    echo [OK] Recipe Service - Running
) else (
    echo [X] Recipe Service - Not Running
)

echo Checking Plan Service (8084):
netstat -ano | findstr :8084 >nul
if %errorlevel% equ 0 (
    echo [OK] Plan Service - Running
) else (
    echo [X] Plan Service - Not Running
)

echo Checking Admin Service (8085):
netstat -ano | findstr :8085 >nul
if %errorlevel% equ 0 (
    echo [OK] Admin Service - Running
) else (
    echo [X] Admin Service - Not Running
)

echo Checking AIGC Service (8086):
netstat -ano | findstr :8086 >nul
if %errorlevel% equ 0 (
    echo [OK] AIGC Service - Running
) else (
    echo [X] AIGC Service - Not Running
)

echo.
echo [3] API Health Check:
echo ========================================

echo Testing Gateway Service API:
curl -s http://localhost:8080/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Gateway API - Responding
) else (
    echo [X] Gateway API - No Response
)

echo Testing User Service API:
curl -s http://localhost:8082/health-profile/1 >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] User API - Responding
) else (
    echo [X] User API - No Response
)

echo Testing Recipe Service API:
curl -s http://localhost:8083/recipes >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Recipe API - Responding
) else (
    echo [X] Recipe API - No Response
)

echo Testing AIGC Service API:
curl -s http://localhost:8086/aigc/task/test >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] AIGC API - Responding
) else (
    echo [X] AIGC API - No Response
)

echo.
echo [4] Quick Access Links:
echo ========================================
echo Nacos Console: http://localhost:8848/nacos
echo Gateway Entry: http://localhost:8080
echo.

pause
