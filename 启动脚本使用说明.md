# 健康饮食平台启动脚本使用说明

## 📋 脚本列表

### 🚀 启动脚本
- **`start-complete-platform.bat`** - 完整平台启动（推荐）
- **`start-all-microservices.bat`** - 仅启动微服务
- **`docker-compose up -d`** - 仅启动Docker基础设施

### 🛑 停止脚本  
- **`stop-all-microservices.bat`** - 停止所有微服务
- **`docker-compose down`** - 停止Docker基础设施

### 🔍 检查脚本
- **`check-services-status.bat`** - 检查所有服务状态

## 🎯 推荐使用流程

### 首次启动
1. **启动完整平台**
   ```bash
   双击运行: start-complete-platform.bat
   ```

2. **配置Nacos（首次必须）**
   - 访问: http://localhost:8848/nacos
   - 用户名/密码: nacos/nacos
   - 在配置管理中创建以下配置文件：
     - `gateway-service.yml`
     - `auth-service.yml`
     - `user-service.yml`
     - `recipe-service.yml`
     - `plan-service.yml`
     - `admin-service.yml`
     - `aigc-service.yml`

3. **检查服务状态**
   ```bash
   双击运行: check-services-status.bat
   ```

### 日常使用
1. **快速启动所有服务**
   ```bash
   双击运行: start-complete-platform.bat
   ```

2. **停止所有服务**
   ```bash
   双击运行: stop-all-microservices.bat
   ```

## 🏗️ 服务架构

### 基础设施服务 (Docker)
- **MySQL**: localhost:3307
- **Redis**: localhost:6380
- **Nacos**: localhost:8848

### 微服务应用
- **Gateway Service**: localhost:8080 (API网关)
- **Auth Service**: localhost:8081 (认证服务)
- **User Service**: localhost:8082 (用户服务)
- **Recipe Service**: localhost:8083 (食谱服务)
- **Plan Service**: localhost:8084 (计划服务)
- **Admin Service**: localhost:8085 (管理服务)
- **AIGC Service**: localhost:8086 (AI服务)

## 🔧 故障排除

### 常见问题
1. **端口被占用**
   - 运行 `stop-all-microservices.bat` 停止所有服务
   - 或手动检查端口: `netstat -ano | findstr :8080`

2. **Docker服务启动失败**
   - 确保Docker Desktop正在运行
   - 检查端口3307、6380、8848是否被占用

3. **微服务启动失败**
   - 检查Nacos配置是否正确
   - 确保Docker基础设施服务正常运行

4. **Nacos配置丢失**
   - 重新在Nacos控制台中创建配置文件
   - 参考现有的配置文件模板

### 调试命令
```bash
# 检查Docker服务
docker-compose ps

# 检查端口占用
netstat -ano | findstr :8080

# 检查Java进程
tasklist | findstr java

# 测试API
curl http://localhost:8080/actuator/health
```

## 📝 注意事项

1. **启动顺序很重要**
   - 先启动Docker基础设施
   - 再启动微服务应用

2. **等待时间**
   - Docker服务需要30秒启动
   - 微服务需要2-3分钟完全启动

3. **配置管理**
   - 所有配置都在Nacos中管理
   - 修改配置后会自动热更新

4. **资源要求**
   - 建议至少8GB内存
   - 确保有足够的磁盘空间

## 🎉 成功标志

当所有服务正常启动后，您应该能够：
- 访问Nacos控制台看到7个注册的服务
- 通过Gateway访问各个微服务API
- 在服务列表中看到所有服务状态为UP

祝您使用愉快！🚀
