# ===============================================
# 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8085 # 应用的监听端口

spring:
  application:
    name: admin-service # 定义应用名，用于服务注册和配置拉取

  # --- 核心修改：将 'import' 放在 'spring.config' 下 ---
  config:
    import:
      # 告诉Spring Boot去Nacos加载一个名为 'admin-service.yml' 的配置文件
      - nacos:${spring.application.name}.yml

  # --- Nacos客户端的地址配置 ---
  cloud:
    nacos:
      # 服务发现中心的地址
      discovery:
        server-addr: nacos:8848
      # 配置中心的地址
      config:
        server-addr: nacos:8848
        # file-extension: yml #  在 Spring Boot 3.x/Spring Cloud 2022+ 中，可以直接在import中指定后缀

# --- 本地固定的日志配置 ---
logging:
  level:
    com.healthdiet.admin: debug
    org.springframework: info # 一般设为 info，需要时再开 debug