# ===============================================
# 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8085 # 应用的监听端口

spring:
  application:
    name: admin-service # 定义应用名，用于服务注册和配置拉取

  # --- 导入Nacos配置 ---
  config:
    import: "nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: true  # 启用服务注册
      config:
        server-addr: localhost:8848

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    com.healthdiet.admin: debug
    com.baomidou.mybatisplus: debug

management:
  endpoints:
    web:
      exposure:
        include: "*"