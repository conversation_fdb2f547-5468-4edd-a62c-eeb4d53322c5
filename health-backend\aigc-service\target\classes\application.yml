# ===============================================
# aigc-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8085 # AIGC服务的监听端口

spring:
  application:
    name: aigc-service # 应用名

  # --- 导入Nacos配置 ---
  config:
    import:
      # 去Nacos加载一个名为 'aigc-service.yml' 的配置文件
      - nacos:${spring.application.name}.yml

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848
      config:
        server-addr: nacos:8848

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    # 你的项目包名
    com.healthdiet.aigc: debug
    org.springframework.amqp: info # RabbitMQ日志通常设为info
management:
  endpoints:
    web:
      exposure:
        include: "*" # 暴露所有监控端点