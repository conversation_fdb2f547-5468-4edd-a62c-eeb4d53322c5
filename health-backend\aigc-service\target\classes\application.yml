# ===============================================
# aigc-service 本地 application.yml (引导配置)
# ===============================================
server:
  port: 8086 # AIGC服务的监听端口

spring:
  application:
    name: aigc-service # 应用名

  # --- 导入Nacos配置 ---
  config:
    import: "optional:nacos:${spring.application.name}.yml"

  # --- Nacos客户端地址 ---
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: false  # 暂时禁用服务注册
      config:
        server-addr: localhost:8848

  # --- 临时本地数据库配置 ---
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: mysql123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6380
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5673
    username: guest
    password: guest

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# DeepSeek API配置（本地备用配置）
deepseek:
  api:
    key: "your-deepseek-api-key-here"
    base-url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    timeout: 30000
    max-tokens: 2000

# --- 本地固定的日志和监控配置 ---
logging:
  level:
    # 你的项目包名
    com.healthdiet.aigc: debug
    com.baomidou.mybatisplus: debug
    org.springframework.amqp: info # RabbitMQ日志通常设为info
    org.springframework.data.redis: debug

management:
  endpoints:
    web:
      exposure:
        include: "*" # 暴露所有监控端点