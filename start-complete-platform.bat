@echo off
chcp 65001 >nul
echo ========================================
echo 启动完整的健康饮食平台
echo ========================================

echo.
echo [阶段 1/2] 启动Docker基础设施服务...
echo ========================================

echo 启动 MySQL, Redis, Nacos 容器...
docker-compose up -d

echo.
echo 等待基础设施服务启动完成...
timeout /t 30 /nobreak

echo.
echo 检查Docker服务状态:
docker-compose ps

echo.
echo [阶段 2/2] 启动微服务应用...
echo ========================================

echo.
echo 重要提示: 
echo 1. 请确保已在Nacos中配置好所有服务的配置文件
echo 2. Nacos控制台: http://localhost:8848/nacos (nacos/nacos)
echo 3. 如果是首次启动，请先配置Nacos再继续
echo.

set /p continue="是否继续启动微服务? (Y/N): "
if /i "%continue%" neq "Y" (
    echo 已取消微服务启动
    goto :end
)

echo.
echo 开始启动微服务...

echo.
echo [1/7] 启动 Gateway Service (端口: 8080)...
cd /d "%~dp0health-backend\gateway-service"
start "Gateway Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [2/7] 启动 Auth Service (端口: 8081)...
cd /d "%~dp0health-backend\auth-service"
start "Auth Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [3/7] 启动 User Service (端口: 8082)...
cd /d "%~dp0health-backend\user-service"
start "User Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [4/7] 启动 Recipe Service (端口: 8083)...
cd /d "%~dp0health-backend\recipe-service"
start "Recipe Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [5/7] 启动 Plan Service (端口: 8084)...
cd /d "%~dp0health-backend\plan-service"
start "Plan Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [6/7] 启动 Admin Service (端口: 8085)...
cd /d "%~dp0health-backend\admin-service"
start "Admin Service" cmd /k "mvn spring-boot:run"
timeout /t 8 /nobreak >nul

echo [7/7] 启动 AIGC Service (端口: 8086)...
cd /d "%~dp0health-backend\aigc-service"
start "AIGC Service" cmd /k "mvn spring-boot:run"

echo.
echo ========================================
echo 平台启动完成！
echo ========================================
echo.
echo 基础设施服务:
echo - MySQL:    localhost:3307
echo - Redis:    localhost:6380
echo - Nacos:    localhost:8848
echo.
echo 微服务应用:
echo - Gateway:  localhost:8080
echo - Auth:     localhost:8081
echo - User:     localhost:8082
echo - Recipe:   localhost:8083
echo - Plan:     localhost:8084
echo - Admin:    localhost:8085
echo - AIGC:     localhost:8086
echo.
echo 管理界面:
echo - Nacos控制台: http://localhost:8848/nacos
echo.
echo 请等待2-3分钟让所有服务完全启动...
echo.

:end
pause
