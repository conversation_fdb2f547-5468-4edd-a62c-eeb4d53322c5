@echo off
echo ========================================
echo Starting All Microservices
echo ========================================

echo.
echo Checking Docker service status...
docker-compose ps

echo.
echo ========================================
echo Starting microservices in order...
echo ========================================

echo.
echo [1/7] Starting Gateway Service (Port: 8080)...
cd /d "%~dp0health-backend\gateway-service"
start "Gateway Service" cmd /k "mvn spring-boot:run"
echo Gateway Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [2/7] Starting Auth Service (Port: 8081)...
cd /d "%~dp0health-backend\auth-service"
start "Auth Service" cmd /k "mvn spring-boot:run"
echo Auth Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [3/7] Starting User Service (Port: 8082)...
cd /d "%~dp0health-backend\user-service"
start "User Service" cmd /k "mvn spring-boot:run"
echo User Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [4/7] Starting Recipe Service (Port: 8083)...
cd /d "%~dp0health-backend\recipe-service"
start "Recipe Service" cmd /k "mvn spring-boot:run"
echo Recipe Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [5/7] Starting Plan Service (Port: 8084)...
cd /d "%~dp0health-backend\plan-service"
start "Plan Service" cmd /k "mvn spring-boot:run"
echo Plan Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [6/7] Starting Admin Service (Port: 8085)...
cd /d "%~dp0health-backend\admin-service"
start "Admin Service" cmd /k "mvn spring-boot:run"
echo Admin Service starting...
timeout /t 5 /nobreak >nul

echo.
echo [7/7] Starting AIGC Service (Port: 8086)...
cd /d "%~dp0health-backend\aigc-service"
start "AIGC Service" cmd /k "mvn spring-boot:run"
echo AIGC Service starting...

echo.
echo ========================================
echo All microservice startup commands executed
echo ========================================
echo.
echo Services need time to start, please wait 2-3 minutes...
echo You can check service status through:
echo.
echo 1. Nacos Console: http://localhost:8848/nacos
echo    Username/Password: nacos/nacos
echo.
echo 2. Service port check:
echo    - Gateway Service:  http://localhost:8080
echo    - Auth Service:     http://localhost:8081
echo    - User Service:     http://localhost:8082
echo    - Recipe Service:   http://localhost:8083
echo    - Plan Service:     http://localhost:8084
echo    - Admin Service:    http://localhost:8085
echo    - AIGC Service:     http://localhost:8086
echo.
echo 3. Health check command:
echo    curl http://localhost:8080/actuator/health
echo.
echo ========================================

pause
