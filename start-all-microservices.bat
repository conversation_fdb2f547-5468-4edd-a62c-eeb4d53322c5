@echo off
chcp 65001 >nul
echo ========================================
echo 启动健康饮食平台所有微服务
echo ========================================

echo.
echo 检查Docker服务状态...
docker-compose ps

echo.
echo ========================================
echo 开始启动微服务 (按顺序启动)
echo ========================================

echo.
echo [1/7] 启动 Gateway Service (端口: 8080)...
cd /d "%~dp0health-backend\gateway-service"
start "Gateway Service" cmd /k "mvn spring-boot:run"
echo Gateway Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [2/7] 启动 Auth Service (端口: 8081)...
cd /d "%~dp0health-backend\auth-service"
start "Auth Service" cmd /k "mvn spring-boot:run"
echo Auth Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [3/7] 启动 User Service (端口: 8082)...
cd /d "%~dp0health-backend\user-service"
start "User Service" cmd /k "mvn spring-boot:run"
echo User Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [4/7] 启动 Recipe Service (端口: 8083)...
cd /d "%~dp0health-backend\recipe-service"
start "Recipe Service" cmd /k "mvn spring-boot:run"
echo Recipe Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [5/7] 启动 Plan Service (端口: 8084)...
cd /d "%~dp0health-backend\plan-service"
start "Plan Service" cmd /k "mvn spring-boot:run"
echo Plan Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [6/7] 启动 Admin Service (端口: 8085)...
cd /d "%~dp0health-backend\admin-service"
start "Admin Service" cmd /k "mvn spring-boot:run"
echo Admin Service 启动中...
timeout /t 5 /nobreak >nul

echo.
echo [7/7] 启动 AIGC Service (端口: 8086)...
cd /d "%~dp0health-backend\aigc-service"
start "AIGC Service" cmd /k "mvn spring-boot:run"
echo AIGC Service 启动中...

echo.
echo ========================================
echo 所有微服务启动命令已执行
echo ========================================
echo.
echo 服务启动需要时间，请等待约2-3分钟...
echo 您可以通过以下方式检查服务状态：
echo.
echo 1. Nacos控制台: http://localhost:8848/nacos
echo    用户名/密码: nacos/nacos
echo.
echo 2. 各服务端口检查:
echo    - Gateway Service:  http://localhost:8080
echo    - Auth Service:     http://localhost:8081  
echo    - User Service:     http://localhost:8082
echo    - Recipe Service:   http://localhost:8083
echo    - Plan Service:     http://localhost:8084
echo    - Admin Service:    http://localhost:8085
echo    - AIGC Service:     http://localhost:8086
echo.
echo 3. 健康检查命令:
echo    curl http://localhost:8080/actuator/health
echo.
echo ========================================

pause
